# 📋 Document Rules & Workflow - MCP Multi-Agent Project

## 🎯 Purpose

This document establishes the rules, standards, and workflows that ALL agents must follow when working on the MCP Multi-Agent project. It ensures consistency, maintains project knowledge, and preserves context across all development sessions.

## 🔒 CRITICAL RULES - NEVER VIOLATE

### **Rule 1: ALWAYS Check Existing Documentation First**
Before creating ANY new documentation or making changes:
1. ✅ Read `docs/PROJECT_PROGRESS.md` for current status
2. ✅ Check `docs/README.md` for documentation index
3. ✅ Review relevant existing documents
4. ✅ Understand the current phase and task context

### **Rule 2: MANDATORY Handoff Documentation**
When completing ANY task with priority ≥ 0:
1. ✅ MUST create `PHASE_X_TASK_Y_COMPLETION_HANDOFF.md`
2. ✅ MUST update `PROJECT_PROGRESS.md`
3. ✅ MUST update task status in Archon system
4. ✅ MUST include technical details, testing results, and next steps

### **Rule 3: Context Preservation**
ALL agents MUST:
1. ✅ Reference project ID: `3d6353d3-caac-488c-8168-00f924dd6776`
2. ✅ Maintain technology stack: TypeScript/Node.js, mcp-use v0.1.15, OpenAI GPT-4
3. ✅ Follow established patterns and architecture
4. ✅ Preserve all existing functionality

## 📁 Document Structure & Hierarchy

### **Core Documentation (NEVER DELETE)**
```
docs/
├── README.md                    # Documentation index and navigation
├── PRODUCT_BRIEF.md            # Project overview and goals
├── USER_GUIDE.md               # Complete user documentation
├── API_REFERENCE.md            # Comprehensive API documentation
├── DEVELOPMENT_GUIDE.md        # Contributing and development setup
├── ARCHITECTURE.md             # Technical architecture details
├── PROJECT_PROGRESS.md         # Current status and roadmap
├── BUG_LOG.md                  # Issue tracking and resolutions
└── DOCUMENT_RULES.md           # This file - workflow rules
```

### **Handoff Documentation (APPEND ONLY)**
```
docs/
├── PHASE_1_COMPLETION_HANDOFF.md
├── PHASE_2_TASK_1_COMPLETION_HANDOFF.md
├── PHASE_2_TASK_2_COMPLETION_HANDOFF.md
├── PHASE_2_TASK_3_COMPLETION_HANDOFF.md
└── PHASE_X_TASK_Y_COMPLETION_HANDOFF.md  # Future handoffs
```

## 🔄 Documentation Workflow

### **Starting a New Task**
1. **Read Current Status**:
   ```bash
   # Check these documents in order:
   1. docs/PROJECT_PROGRESS.md - Current status
   2. docs/ARCHITECTURE.md - Technical context
   3. Latest PHASE_*_COMPLETION_HANDOFF.md - Previous work
   ```

2. **Update Task Status**:
   ```bash
   archon:manage_task(action="update", task_id="...", update_fields={"status": "doing"})
   ```

3. **Research Context**:
   ```bash
   archon:perform_rag_query(query="relevant technical context", match_count=5)
   archon:search_code_examples(query="implementation patterns", match_count=3)
   ```

### **During Task Execution**
1. **Document Issues**: Add to `BUG_LOG.md` if bugs are found/fixed
2. **Update Architecture**: Modify `ARCHITECTURE.md` if architecture changes
3. **Test Thoroughly**: Follow testing patterns in `DEVELOPMENT_GUIDE.md`

### **Completing a Task**
1. **Create Handoff Document**:
   ```
   File: docs/PHASE_X_TASK_Y_COMPLETION_HANDOFF.md
   Template: Use existing handoff documents as templates
   Required Sections:
   - Task completion summary
   - Technical implementation details
   - Testing results
   - Next phase preparation
   - Archon workflow commands
   ```

2. **Update Progress Document**:
   ```
   File: docs/PROJECT_PROGRESS.md
   Updates Required:
   - Move task from "In Progress" to "Completed"
   - Update progress percentages
   - Update phase completion status
   - Add achievement highlights
   ```

3. **Update Task Status**:
   ```bash
   archon:manage_task(action="update", task_id="...", update_fields={"status": "review"})
   ```

## 📝 Documentation Standards

### **File Naming Convention**
- **Core Docs**: `UPPERCASE_WITH_UNDERSCORES.md`
- **Handoffs**: `PHASE_X_TASK_Y_COMPLETION_HANDOFF.md`
- **Temporary**: `TEMP_DESCRIPTION.md` (must be cleaned up)

### **Content Standards**
1. **Headers**: Use emoji + descriptive text (`# 🎯 Purpose`)
2. **Status Indicators**: Use ✅ ❌ 🔄 ⏳ consistently
3. **Code Blocks**: Always specify language for syntax highlighting
4. **Links**: Use relative paths for internal docs (`./OTHER_DOC.md`)
5. **Dates**: Use ISO format (2025-08-17)

### **Required Sections for Handoff Documents**
```markdown
# 🎉 Phase X Task Y Completion - [Task Name] Handoff

## 📋 Project Overview
- Project Name, ID, GitHub Repo, Tech Stack, Session Date

## ✅ Task Completion Summary
- Task details, status, implementation results

## 🛠️ Technical Implementation Details
- Files created/modified, key features implemented

## 🧪 Verification & Testing Results
- Test results table, verification steps

## 🚀 Next Phase Preparation
- Next priority task, requirements, foundation ready

## 📊 Project Status Overview
- Completed tasks, remaining tasks, progress metrics

## 📚 Archon Workflow Commands for Next Session
- Commands for next agent to continue work

## 🎯 Success Criteria Achieved
- Goals met, quality standards

## 🔄 Handoff Summary
- Status, next agent role, readiness assessment
```

## 🔍 Context Management Rules

### **Project Context MUST Include**
1. **Project Identity**:
   - Name: Multiple MCP Servers General Purpose Agent
   - ID: `3d6353d3-caac-488c-8168-00f924dd6776`
   - Tech Stack: TypeScript/Node.js, mcp-use v0.1.15, OpenAI GPT-4

2. **Current Status** (check `PROJECT_PROGRESS.md`):
   - Overall completion percentage
   - Current phase and task
   - Recently completed work

3. **Architecture Context** (check `ARCHITECTURE.md`):
   - Completed components (✅)
   - In-progress components (🔄)
   - Planned components (⏳)

### **Knowledge Preservation**
1. **Bug Tracking**: ALL bugs/issues MUST be documented in `BUG_LOG.md`
2. **Decision Tracking**: Architecture decisions MUST be in `ARCHITECTURE.md`
3. **Progress Tracking**: Task completion MUST update `PROJECT_PROGRESS.md`
4. **Handoff Tracking**: Task completion MUST create handoff document

## 🚨 Error Prevention Rules

### **NEVER Do These Things**
1. ❌ Delete or overwrite existing handoff documents
2. ❌ Remove completed task entries from `PROJECT_PROGRESS.md`
3. ❌ Change project ID or core technology stack
4. ❌ Skip creating handoff documentation for completed tasks
5. ❌ Modify existing API without updating `API_REFERENCE.md`

### **ALWAYS Do These Things**
1. ✅ Read existing documentation before starting
2. ✅ Update `PROJECT_PROGRESS.md` when completing tasks
3. ✅ Create handoff documents for all major task completions
4. ✅ Test all code changes thoroughly
5. ✅ Update relevant documentation when making changes

## 🔄 Multi-Agent Coordination

### **Agent Handoff Protocol**
1. **Outgoing Agent**:
   - Creates handoff document
   - Updates project progress
   - Sets task status to "review"
   - Provides clear next steps

2. **Incoming Agent**:
   - Reads handoff document
   - Reviews project progress
   - Understands current context
   - Continues from documented state

### **Agent-Specific Rules**

#### **Documentation Specialist**
- MUST update `docs/README.md` when adding new documents
- MUST maintain cross-references between documents
- MUST ensure all examples are tested and accurate

#### **Backend Developer**
- MUST update `API_REFERENCE.md` when changing APIs
- MUST update `ARCHITECTURE.md` when changing structure
- MUST create comprehensive tests for new features

#### **Code Reviewer**
- MUST document security findings in handoff documents
- MUST update `BUG_LOG.md` if issues are found
- MUST verify all documentation is accurate

## 📊 Quality Assurance

### **Documentation Quality Checklist**
- [ ] All code examples are tested and working
- [ ] Cross-references are accurate and up-to-date
- [ ] Handoff documents follow the required template
- [ ] Project progress is accurately reflected
- [ ] No broken internal links

### **Context Integrity Checklist**
- [ ] Project ID is consistent across all documents
- [ ] Technology stack is accurately represented
- [ ] Current status matches actual implementation
- [ ] All completed tasks are properly documented
- [ ] Next steps are clearly defined

## 🔧 Maintenance Schedule

### **After Each Task Completion**
1. Update `PROJECT_PROGRESS.md`
2. Create handoff document
3. Update relevant core documentation

### **After Each Phase Completion**
1. Review and update `ARCHITECTURE.md`
2. Update `USER_GUIDE.md` if user-facing changes
3. Update `API_REFERENCE.md` if API changes
4. Review `docs/README.md` for navigation accuracy

### **Monthly Review**
1. Review all documentation for accuracy
2. Update external links and references
3. Consolidate and clean up temporary documents
4. Verify all examples still work

## 🎯 Success Metrics

### **Documentation Success**
- All tasks have corresponding handoff documents
- Project progress is accurately tracked
- No broken internal references
- All code examples are functional

### **Context Preservation Success**
- New agents can quickly understand current state
- No loss of project knowledge between sessions
- Consistent architecture and patterns maintained
- Clear audit trail of all changes

---

**🔒 ENFORCEMENT**: These rules are MANDATORY for ALL agents working on this project. Violation of these rules will result in context loss, documentation inconsistency, and project knowledge degradation.

**📞 QUESTIONS**: If any rule is unclear, refer to existing handoff documents as examples or ask for clarification before proceeding.

**🚀 GOAL**: Maintain professional-grade documentation that preserves project knowledge and enables seamless multi-agent collaboration.

---

*Created: 2025-08-17*  
*Version: 1.0*  
*Status: ACTIVE - All agents must follow these rules*
