# 🏗️ MCP Multi-Agent Architecture

## Overview

The **Multiple MCP Servers General Purpose Agent** is a TypeScript-based AI agent system that connects to multiple Model Context Protocol (MCP) servers simultaneously. It provides intelligent server selection, robust connection management, and seamless OpenAI integration for natural language processing tasks.

## 🎯 Core Design Principles

- **Modularity**: Clean separation of concerns with dedicated modules for each responsibility
- **Scalability**: Support for multiple concurrent MCP server connections
- **Reliability**: Robust error handling, health monitoring, and automatic reconnection
- **Type Safety**: Full TypeScript implementation with comprehensive type definitions
- **Extensibility**: Plugin-like architecture for easy addition of new MCP servers

## 🏛️ High-Level Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    MCP Multi-Agent                          │
├─────────────────────────────────────────────────────────────┤
│  CLI Interface (Commander.js)                               │
├─────────────────────────────────────────────────────────────┤
│  Agent Core (Future Implementation)                         │
├─────────────────────────────────────────────────────────────┤
│  Server Manager                    │  LLM Factory           │
│  - Health Monitoring               │  - OpenAI Client       │
│  - Connection Management           │  - Client Caching      │
│  - Auto Reconnection               │  - Connection Testing  │
├─────────────────────────────────────────────────────────────┤
│  Configuration System                                       │
│  - Type Definitions                                         │
│  - Environment Loading                                      │
│  - Example Configurations                                   │
├─────────────────────────────────────────────────────────────┤
│  MCP Servers (via mcp-use library)                         │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │ Filesystem  │ │   Browser   │ │   SQLite    │ ...      │
│  │   Server    │ │   Server    │ │   Server    │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
└─────────────────────────────────────────────────────────────┘
```

## 📁 Directory Structure

```
src/
├── agent/              # Core agent implementation ✅ COMPLETED
│   ├── multi-server-agent.ts # MultiServerAgent class with MCPAgent integration
│   ├── test-agent.ts   # Comprehensive testing suite
│   └── index.ts        # Agent exports and type definitions
├── cli/                # Command-line interface (planned)
├── config/             # Configuration management ✅ COMPLETED
│   ├── types.ts        # TypeScript type definitions
│   ├── loader.ts       # Configuration loading logic
│   ├── examples.ts     # Example server configurations
│   ├── client-factory.ts # MCP client factory ✅ COMPLETED
│   └── index.ts        # Configuration exports
├── llm/                # OpenAI LLM integration ✅ COMPLETED
│   ├── factory.ts      # LLM client factory and management
│   ├── openai-client.ts # OpenAI client wrapper
│   ├── test-integration.ts # Integration testing
│   └── index.ts        # LLM exports
├── monitoring/         # Health monitoring (planned)
├── utils/              # Utility functions (planned)
└── index.ts            # Main entry point with CLI commands
```

## 🔧 Core Components

### 1. Configuration System (`src/config/`) ✅ COMPLETED

**Purpose**: Centralized configuration management with type safety and validation.

**Key Files**:
- `types.ts`: Comprehensive type definitions for all configuration options
- `examples.ts`: Pre-configured server examples and environment templates
- `loader.ts`: Environment variable loading and validation ✅ COMPLETED
- `client-factory.ts`: MCP client factory with connection management ✅ COMPLETED

**Features**:
- Support for multiple connection types (HTTP, WebSocket, stdio, SSE)
- Server priority and tagging system
- Retry configuration with exponential backoff
- Environment-specific configurations (dev, prod, test)

### 2. LLM Integration (`src/llm/`) ✅ COMPLETED

**Purpose**: OpenAI client management with caching and connection testing.

**Key Components**:
- **LLMFactory**: Singleton pattern for client management
- **OpenAIClient**: Wrapper around OpenAI SDK with enhanced features
- **Configuration Loading**: Environment-based configuration with defaults
- **Test Integration**: Comprehensive testing utilities

**Features**:
- Client caching with automatic health checks
- Connection testing and validation
- Configurable retry logic and timeouts
- Support for custom OpenAI endpoints
- Streaming response support with AI SDK integration

### 3. Agent Implementation (`src/agent/`) ✅ COMPLETED

**Purpose**: Multi-server agent orchestration with MCPAgent integration.

**Key Components**:
- **MultiServerAgent**: Main agent class with MCPAgent integration
- **Factory Functions**: Clean agent creation with auto-initialization
- **Test Suite**: Comprehensive testing with CLI commands

**Features**:
- MCPAgent integration with LangChain compatibility
- Server manager enabled for automatic server selection
- Dual response modes: standard and streaming
- Connection testing and server information reporting
- Resource management with proper initialization/shutdown
- Error handling with graceful degradation

### 4. MCP Server Management

**Integration**: Uses the `mcp-use` TypeScript library for MCP server connections.

**Supported Server Types**:
- **Filesystem Server**: File operations and directory management
- **Browser Server**: Web scraping and automation via Puppeteer
- **SQLite Server**: Database operations and SQL queries
- **Memory Server**: Persistent knowledge storage
- **Git Server**: Version control operations
- **Custom HTTP/WebSocket Servers**: Extensible server support

## 🔌 Connection Architecture

### MCP Connection Types

1. **stdio**: Process-based communication (most common)
   - Spawns server as child process
   - JSON-RPC over stdin/stdout
   - Used by: filesystem, browser, sqlite, memory, git servers

2. **HTTP**: RESTful API communication
   - Standard HTTP requests with JSON payloads
   - Custom headers and authentication support
   - Used by: custom API servers

3. **WebSocket**: Real-time bidirectional communication
   - Persistent connections for real-time updates
   - Used by: custom real-time servers

4. **SSE**: Server-Sent Events (planned)
   - One-way streaming from server to client
   - Used by: streaming data servers

### Server Selection Strategy

```typescript
interface ServerSelectionCriteria {
  priority: number;        // Higher = more preferred
  tags: string[];         // Capability matching
  health: boolean;        // Server availability
  responseTime: number;   // Performance metrics
}
```

## 🔄 Data Flow

### 1. Initialization Flow
```
1. Load Environment Variables
2. Parse Configuration
3. Initialize LLM Factory
4. Start MCP Server Manager
5. Connect to Enabled Servers
6. Begin Health Monitoring
```

### 2. Request Processing Flow
```
1. User Input → CLI Interface
2. Parse Intent → Agent Core
3. Select Appropriate Server(s) → Server Manager
4. Execute MCP Tools → mcp-use Library
5. Process Results → LLM Integration
6. Generate Response → OpenAI Client
7. Return to User → CLI Interface
```

## 🛡️ Error Handling & Resilience

### Connection Management
- **Automatic Reconnection**: Failed servers automatically attempt reconnection
- **Health Monitoring**: Periodic health checks with configurable intervals
- **Graceful Degradation**: System continues operating with available servers
- **Circuit Breaker Pattern**: Prevents cascading failures

### Retry Strategy
```typescript
interface RetryConfig {
  maxAttempts: number;
  delayMs: number;
  backoffMultiplier?: number; // Exponential backoff
}
```

### Error Categories
1. **Connection Errors**: Network failures, timeouts
2. **Authentication Errors**: API key issues, authorization failures
3. **Server Errors**: MCP server crashes, invalid responses
4. **Configuration Errors**: Invalid settings, missing requirements

## 🔍 Monitoring & Observability

### Health Monitoring (Planned)
- Server availability tracking
- Response time metrics
- Error rate monitoring
- Connection pool status

### Logging Strategy
- **Levels**: debug, info, warn, error
- **Formats**: JSON (production), text (development)
- **Destinations**: Console, file, external services

## 🚀 Deployment Architecture

### Environment Configurations

**Development**:
- Verbose logging enabled
- Extended timeouts for debugging
- Limited server connections
- Local file-based storage

**Production**:
- Optimized performance settings
- JSON logging for parsing
- Full server complement
- External monitoring integration

**Testing**:
- Minimal server set
- Fast timeouts
- Mock server support
- Isolated environments

## 🔮 Future Enhancements

### Phase 2: Core Agent Implementation
- Multi-step reasoning engine
- Tool selection optimization
- Context management
- Memory persistence

### Phase 3: Advanced Features
- Server load balancing
- Caching layer
- Plugin system
- Custom tool development

### Phase 4: User Interface
- Interactive chat mode
- Web-based dashboard
- Real-time monitoring
- Configuration GUI

## 🔗 Dependencies

### Core Dependencies
- `mcp-use`: MCP server integration library
- `openai`: OpenAI API client
- `@ai-sdk/openai`: AI SDK OpenAI provider
- `commander`: CLI framework
- `chalk`: Terminal styling

### Development Dependencies
- `typescript`: Type checking and compilation
- `vitest`: Testing framework
- `eslint`: Code linting
- `tsx`: TypeScript execution

## 📊 Performance Considerations

### Optimization Strategies
- **Connection Pooling**: Reuse MCP connections
- **Client Caching**: Cache OpenAI clients
- **Lazy Loading**: Load servers on demand
- **Concurrent Processing**: Parallel server operations

### Resource Management
- **Memory**: Efficient client caching with cleanup
- **CPU**: Asynchronous operations throughout
- **Network**: Connection reuse and timeout management
- **Storage**: Minimal local storage requirements

---

*This architecture document reflects the current implementation state and planned future enhancements. The system is designed for extensibility and maintainability while providing robust multi-server MCP integration.*
