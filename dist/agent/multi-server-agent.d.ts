/**
 * Multi-server MCP Agent implementation
 *
 * This class integrates the mcp-use library's MCPAgent with our OpenAI client
 * and configuration system to provide a unified interface for interacting
 * with multiple MCP servers.
 */
import { OpenAIClient } from '@/llm/openai-client.js';
import type { MCPMultiAgentConfig } from '@/config/types.js';
/**
 * Options for running agent queries
 */
export interface AgentRunOptions {
    /** Maximum number of steps the agent can take */
    maxSteps?: number;
    /** Timeout in milliseconds */
    timeout?: number;
    /** Whether to stream the response */
    stream?: boolean;
    /** Additional context or instructions */
    context?: string;
    /** Specific servers to use (if not provided, uses all enabled servers) */
    servers?: string[];
}
/**
 * Result from agent execution
 */
export interface AgentResult {
    /** The final response from the agent */
    response: string;
    /** Number of steps taken */
    steps: number;
    /** Execution time in milliseconds */
    executionTime: number;
    /** Tools that were used during execution */
    toolsUsed: string[];
    /** Any errors encountered (non-fatal) */
    warnings?: string[];
}
/**
 * Multi-server MCP Agent that combines OpenAI LLM with multiple MCP servers
 */
export declare class MultiServerAgent {
    private config;
    private clientFactory;
    private openaiClient;
    private mcpAgent;
    private langchainLLM;
    private initialized;
    constructor(config: MCPMultiAgentConfig, openaiClient: OpenAIClient);
    /**
     * Initialize the agent with MCP servers and LLM
     */
    initialize(): Promise<void>;
    /**
     * Run a query using the multi-server agent
     */
    run(query: string, options?: AgentRunOptions): Promise<AgentResult>;
    /**
     * Run a streaming query (using our OpenAI client for streaming)
     */
    runStream(query: string, options?: AgentRunOptions, onChunk?: (chunk: string) => void): Promise<AgentResult>;
    /**
     * Get information about available servers and tools
     */
    getServerInfo(): Promise<{
        servers: Array<{
            id: string;
            name: string;
            description: string;
            enabled: boolean;
            connectionType: string;
        }>;
        totalServers: number;
        enabledServers: number;
    }>;
    /**
     * Test connection to all enabled servers
     */
    testConnections(): Promise<{
        successful: string[];
        failed: Array<{
            serverId: string;
            error: string;
        }>;
    }>;
    /**
     * Gracefully shutdown the agent and close all connections
     */
    shutdown(): Promise<void>;
    /**
     * Check if the agent is initialized and ready
     */
    isReady(): boolean;
    /**
     * Get the current configuration
     */
    getConfig(): MCPMultiAgentConfig;
}
/**
 * Factory function to create a MultiServerAgent with proper configuration
 */
export declare function createMultiServerAgent(config: MCPMultiAgentConfig, openaiClient: OpenAIClient): Promise<MultiServerAgent>;
//# sourceMappingURL=multi-server-agent.d.ts.map